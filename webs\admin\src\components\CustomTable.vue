<template>
  <div class="table-container">
    <!-- 表格头部 -->
    <div class="table-header" v-if="showHeader">
      <div class="table-title">{{ title }}</div>
      <div class="table-actions">
        <slot name="actions"></slot>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div class="table-search" v-if="showSearch">
      <el-form :model="searchForm" inline>
        <el-form-item
          v-for="field in searchFields"
          :key="field.prop"
          :label="field.label"
        >
          <el-input
            v-if="field.type === 'input'"
            v-model="searchForm[field.prop]"
            :placeholder="field.placeholder"
            clearable
          />
          <el-select
            v-else-if="field.type === 'select'"
            v-model="searchForm[field.prop]"
            :placeholder="field.placeholder"
            clearable
            style="min-width: 150px; width: 100%"
          >
            <el-option
              v-for="option in field.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          <el-date-picker
            v-else-if="field.type === 'date'"
            v-model="searchForm[field.prop]"
            type="date"
            :placeholder="field.placeholder"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格 -->
    <el-table
      :data="tableData"
      :loading="loading"
      :element-loading-text="loadingText"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
      v-bind="$attrs"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      class="custom-table"
    >
      <el-table-column v-if="showSelection" type="selection" width="55" />

      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth"
        :sortable="column.sortable"
        :formatter="column.formatter"
        :show-overflow-tooltip="column.showOverflowTooltip !== false"
      >
        <template #default="{ row, column: col, $index }" v-if="column.slot">
          <slot
            :name="column.prop"
            :row="row"
            :column="col"
            :index="$index"
          ></slot>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="table-pagination" v-if="showPagination">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="pageSizes"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from "vue";

// Props定义
const props = defineProps({
  // 表格标题
  title: {
    type: String,
    default: ""
  },
  // 表格数据
  data: {
    type: Array,
    default: () => []
  },
  // 表格列配置
  columns: {
    type: Array,
    required: true
  },
  // 是否显示头部
  showHeader: {
    type: Boolean,
    default: true
  },
  // 是否显示搜索
  showSearch: {
    type: Boolean,
    default: false
  },
  // 搜索字段配置
  searchFields: {
    type: Array,
    default: () => []
  },
  // 是否显示选择框
  showSelection: {
    type: Boolean,
    default: false
  },

  // 是否显示分页
  showPagination: {
    type: Boolean,
    default: true
  },
  // 分页配置
  pagination: {
    type: Object,
    default: () => ({
      page: 1,
      size: 10,
      total: 0
    })
  },
  // 分页大小选项
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100]
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 加载文本
  loadingText: {
    type: String,
    default: '加载中...'
  }
});

// Emits定义
const emit = defineEmits([
  "search",
  "reset",
  "selection-change",
  "sort-change",
  "size-change",
  "current-change"
]);

// 响应式数据
const tableData = ref([]);
const searchForm = reactive({});

// 初始化搜索表单
const initSearchForm = () => {
  props.searchFields.forEach(field => {
    searchForm[field.prop] = "";
  });
};

// 搜索
const handleSearch = () => {
  emit("search", { ...searchForm });
};

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = "";
  });
  emit("reset");
};

// 选择变化
const handleSelectionChange = selection => {
  emit("selection-change", selection);
};

// 排序变化
const handleSortChange = sort => {
  emit("sort-change", sort);
};

// 分页大小变化
const handleSizeChange = size => {
  emit("size-change", size);
};

// 当前页变化
const handleCurrentChange = page => {
  emit("current-change", page);
};

// 监听数据变化
watch(
  () => props.data,
  newData => {
    tableData.value = newData;
  },
  { immediate: true }
);

// 初始化
initSearchForm();
</script>

<style scoped lang="scss">
.table-container {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.table-title {
  font-size: 20px;
  font-weight: 600;
  color: #222;
  letter-spacing: 1px;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.table-search {
  margin-bottom: 24px;
  padding: 16px;
  background: #f7f8fa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.custom-table {
  border-radius: 8px;
  overflow: hidden;
  font-size: 15px;
}

.el-table th {
  background: #fafbfc;
  color: #333;
  font-weight: 600;
}

.el-table .el-button {
  margin: 0 4px;
}

.table-pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
}

.el-dialog__header {
  font-size: 18px;
  font-weight: bold;
  color: #222;
}

.el-dialog__body {
  padding: 24px 16px;
}

.el-dialog__footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

// 响应式设计
@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .table-search {
    padding: 12px;
  }
  .table-search :deep(.el-form--inline .el-form-item) {
    display: block;
    margin-bottom: 12px;
  }
  .table-pagination {
    justify-content: center;
  }
}
</style>
